/**
 * 语音识别兼容性服务
 * 为不同uni-app平台提供统一的语音识别接口
 */

import { config } from './config.js';

class SpeechRecognitionCompatService {
    constructor() {
        this.apiUrl = config.siliconFlow.apiUrl;
        this.apiKey = config.siliconFlow.apiKey;
        this.model = config.siliconFlow.model;
        
        this.isRecording = false;
        this.recordingStartTime = null;
        
        // 检测当前平台
        this.platform = this.detectPlatform();
        console.log('当前平台:', this.platform);
    }

    /**
     * 检测当前运行平台
     */
    detectPlatform() {
        // #ifdef H5
        return 'H5';
        // #endif
        
        // #ifdef MP-WEIXIN
        return 'MP-WEIXIN';
        // #endif
        
        // #ifdef APP-PLUS
        return 'APP-PLUS';
        // #endif
        
        // #ifndef H5 || MP-WEIXIN || APP-PLUS
        return 'UNKNOWN';
        // #endif
    }

    /**
     * 检查兼容性
     */
    checkCompatibility() {
        const result = {
            supported: false,
            issues: [],
            platform: this.platform
        };

        switch (this.platform) {
            case 'H5':
                if (typeof navigator !== 'undefined' && 
                    navigator.mediaDevices && 
                    navigator.mediaDevices.getUserMedia &&
                    window.MediaRecorder) {
                    result.supported = true;
                } else {
                    result.issues.push('浏览器不支持录音API');
                }
                break;
                
            case 'MP-WEIXIN':
                if (typeof uni !== 'undefined' && uni.getRecorderManager) {
                    result.supported = true;
                } else {
                    result.issues.push('微信小程序不支持录音API');
                }
                break;
                
            case 'APP-PLUS':
                if (typeof uni !== 'undefined' && uni.getRecorderManager) {
                    result.supported = true;
                } else {
                    result.issues.push('App环境不支持录音API');
                }
                break;
                
            default:
                result.issues.push('未知平台，不支持录音功能');
        }

        return result;
    }

    /**
     * 开始录音
     */
    async startRecording() {
        const compatibility = this.checkCompatibility();
        if (!compatibility.supported) {
            throw new Error(`当前平台(${this.platform})不支持录音功能: ${compatibility.issues.join(', ')}`);
        }

        this.isRecording = true;
        this.recordingStartTime = Date.now();

        switch (this.platform) {
            case 'H5':
                return await this.startH5Recording();
            case 'MP-WEIXIN':
            case 'APP-PLUS':
                return await this.startUniRecording();
            default:
                throw new Error('不支持的平台');
        }
    }

    /**
     * 停止录音
     */
    async stopRecording() {
        if (!this.isRecording) {
            throw new Error('未在录音状态');
        }

        switch (this.platform) {
            case 'H5':
                return await this.stopH5Recording();
            case 'MP-WEIXIN':
            case 'APP-PLUS':
                return await this.stopUniRecording();
            default:
                throw new Error('不支持的平台');
        }
    }

    /**
     * 取消录音
     */
    cancelRecording() {
        if (!this.isRecording) {
            return;
        }

        this.isRecording = false;
        this.recordingStartTime = null;

        switch (this.platform) {
            case 'H5':
                this.cancelH5Recording();
                break;
            case 'MP-WEIXIN':
            case 'APP-PLUS':
                this.cancelUniRecording();
                break;
        }
    }

    /**
     * H5平台录音实现
     */
    async startH5Recording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });

            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };

            this.mediaRecorder.start();
            return true;

        } catch (error) {
            this.isRecording = false;
            throw error;
        }
    }

    async stopH5Recording() {
        return new Promise((resolve, reject) => {
            this.mediaRecorder.onstop = async () => {
                try {
                    const audioBlob = new Blob(this.audioChunks, { 
                        type: this.mediaRecorder.mimeType 
                    });

                    const result = await this.transcribeAudio(audioBlob);
                    resolve(result);

                } catch (error) {
                    reject(error);
                } finally {
                    this.cleanup();
                }
            };

            this.mediaRecorder.stop();
        });
    }

    cancelH5Recording() {
        if (this.mediaRecorder) {
            this.mediaRecorder.stop();
            this.cleanup();
        }
    }

    /**
     * uni-app平台录音实现
     */
    async startUniRecording() {
        return new Promise((resolve, reject) => {
            this.recorderManager = uni.getRecorderManager();

            const options = {
                duration: 60000,
                sampleRate: 16000,
                numberOfChannels: 1,
                encodeBitRate: 48000,
                format: 'mp3'
            };

            this.recorderManager.onStart(() => {
                console.log('uni录音开始');
                resolve(true);
            });

            this.recorderManager.onError((err) => {
                console.error('uni录音错误', err);
                this.isRecording = false;
                reject(new Error('录音失败: ' + (err.errMsg || '未知错误')));
            });

            this.recorderManager.start(options);
        });
    }

    async stopUniRecording() {
        return new Promise((resolve, reject) => {
            this.recorderManager.onStop(async (res) => {
                try {
                    console.log('uni录音结束', res);

                    if (!res.tempFilePath) {
                        throw new Error('录音文件路径为空');
                    }

                    console.log('录音文件路径:', res.tempFilePath);

                    // 调用云函数进行语音识别
                    const recognitionResult = await this.sendAudioToCloudFunction(res.tempFilePath);
                    console.log('语音识别结果:', recognitionResult);

                    resolve(recognitionResult);

                } catch (error) {
                    console.error('语音识别失败:', error);

                    // 如果云函数调用失败，提供友好的错误提示
                    if (error.message.includes('云函数')) {
                        reject(new Error('语音识别服务暂时不可用，请稍后重试'));
                    } else {
                        reject(error);
                    }
                } finally {
                    this.cleanup();
                }
            });

            this.recorderManager.stop();
        });
    }

    cancelUniRecording() {
        if (this.recorderManager) {
            this.recorderManager.stop();
            this.cleanup();
        }
    }

    /**
     * 调用云函数进行语音识别
     */
    async sendAudioToCloudFunction(filePath) {
        try {
            console.log('准备调用云函数进行语音识别, 文件路径:', filePath);

            // 读取音频文件并转换为base64
            const base64Data = await this.fileToBase64(filePath);
            console.log('音频文件已转换为base64，长度:', base64Data.length);

            // 首先尝试调用云函数
            try {
                const result = await uniCloud.callFunction({
                    name: 'voiceRecognition',
                    data: {
                        audioData: base64Data,
                        audioFormat: 'mp3'  // uni-app录音默认格式
                    }
                });

                console.log('云函数调用结果:', result);

                if (result.result.code !== 200) {
                    throw new Error(result.result.message || '语音识别失败');
                }

                // 返回完整的识别结果，包含解析信息
                return result.result.data;

            } catch (cloudError) {
                console.log('云函数调用失败，尝试直接调用API:', cloudError.message);

                // 如果是本地调试服务连接问题，尝试直接调用API
                if (cloudError.message.includes('无法连接uniCloud本地调试服务') ||
                    cloudError.message.includes('uniCloud')) {
                    console.log('检测到uniCloud连接问题，切换到直接API调用模式');
                    return await this.directApiCall(base64Data, 'mp3');
                } else {
                    throw cloudError;
                }
            }

        } catch (error) {
            console.error('语音识别服务调用失败:', error);
            throw new Error('语音识别服务调用失败: ' + error.message);
        }
    }

    /**
     * 文件路径转base64
     */
    async fileToBase64(filePath) {
        return new Promise((resolve, reject) => {
            // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
            // 小程序环境
            uni.getFileSystemManager().readFile({
                filePath: filePath,
                encoding: 'base64',
                success: (res) => {
                    resolve('data:audio/mp3;base64,' + res.data);
                },
                fail: (error) => {
                    console.error('读取文件失败:', error);
                    reject(new Error('读取音频文件失败: ' + error.errMsg));
                }
            });
            // #endif

            // #ifdef APP-PLUS
            // App环境
            plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
                entry.file((file) => {
                    const reader = new plus.io.FileReader();
                    reader.onload = (e) => {
                        resolve(e.target.result);
                    };
                    reader.onerror = () => {
                        reject(new Error('读取音频文件失败'));
                    };
                    reader.readAsDataURL(file);
                });
            }, (error) => {
                console.error('文件访问失败:', error);
                reject(new Error('文件不存在或无法访问'));
            });
            // #endif
        });
    }

    /**
     * 调用语音识别API（H5环境也使用云函数）
     */
    async transcribeAudio(audioBlob) {
        try {
            console.log('H5环境准备调用云函数进行语音识别');

            // 将Blob转换为base64
            const base64Data = await this.blobToBase64(audioBlob);
            console.log('音频Blob已转换为base64，长度:', base64Data.length);

            // 首先尝试调用云函数
            try {
                const result = await uniCloud.callFunction({
                    name: 'voiceRecognition',
                    data: {
                        audioData: base64Data,
                        audioFormat: 'webm'  // H5录音格式
                    }
                });

                console.log('云函数调用结果:', result);

                if (result.result.code !== 200) {
                    throw new Error(result.result.message || '语音识别失败');
                }

                // 返回完整的识别结果，包含解析信息
                return result.result.data;

            } catch (cloudError) {
                console.log('云函数调用失败，尝试直接调用API:', cloudError.message);

                // 如果是本地调试服务连接问题，尝试直接调用API
                if (cloudError.message.includes('无法连接uniCloud本地调试服务') ||
                    cloudError.message.includes('uniCloud')) {
                    console.log('检测到uniCloud连接问题，切换到直接API调用模式');
                    return await this.directApiCall(base64Data, 'webm');
                } else {
                    throw cloudError;
                }
            }

        } catch (error) {
            console.error('语音识别服务调用失败:', error);
            throw new Error('语音识别服务调用失败: ' + error.message);
        }
    }

    /**
     * 直接调用硅基流动API进行语音识别（备用方案）
     */
    async directApiCall(base64Data, audioFormat) {
        try {
            console.log('使用直接API调用模式进行语音识别');

            // 移除base64前缀（如果存在）
            const cleanBase64 = base64Data.replace(/^data:audio\/[^;]+;base64,/, '');

            // 创建FormData
            const formData = new FormData();

            // 将base64转换为Blob
            const binaryString = atob(cleanBase64);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            const audioBlob = new Blob([bytes], {
                type: audioFormat === 'mp3' ? 'audio/mpeg' : 'audio/webm'
            });

            formData.append('file', audioBlob, `audio.${audioFormat}`);
            formData.append('model', this.model);
            formData.append('response_format', 'json');

            // 发送请求
            const response = await uni.request({
                url: this.apiUrl,
                method: 'POST',
                header: {
                    'Authorization': `Bearer ${this.apiKey}`
                },
                data: formData,
                timeout: 30000
            });

            console.log('直接API调用响应:', response);

            if (response.statusCode !== 200) {
                throw new Error(`API调用失败: ${response.statusCode}`);
            }

            const result = response.data;
            if (!result.text) {
                throw new Error('API返回数据格式错误');
            }

            // 返回与云函数相同格式的数据
            return {
                text: result.text,
                duration: result.duration || 0,
                language: result.language || 'zh',
                parsedInfo: null,  // 直接API调用不包含解析信息
                accurateToday: new Date().toISOString().split('T')[0]  // 使用本地日期
            };

        } catch (error) {
            console.error('直接API调用失败:', error);
            throw new Error('语音识别API调用失败: ' + error.message);
        }
    }

    /**
     * 直接调用硅基流动API进行语音识别（备用方案）
     */
    async directApiCall(base64Data, audioFormat) {
        try {
            console.log('使用直接API调用模式进行语音识别');
            console.log('音频格式:', audioFormat, '数据长度:', base64Data.length);

            // 硅基流动API配置
            const API_KEY = 'sk-gjijzuaoykqwxfpjriihbiiciuhaoemagjtwlceyxgcrwiyl';
            const API_URL = 'https://api.siliconflow.cn/v1/audio/transcriptions';

            // 移除base64前缀（如果存在）
            const cleanBase64 = base64Data.replace(/^data:audio\/[^;]+;base64,/, '');

            // 将base64转换为二进制数据
            const audioBuffer = this.base64ToArrayBuffer(cleanBase64);
            console.log('音频数据大小:', audioBuffer.byteLength, 'bytes');

            // 由于uni-app的限制，我们需要使用不同的方法
            // 这里我们直接使用云函数的逻辑，但在前端实现
            console.log('准备发送请求到硅基流动API');

            // 使用简化的方法：直接调用云函数逻辑
            const response = await this.callSiliconFlowApiDirectly(cleanBase64, audioFormat, API_KEY, API_URL);

            console.log('硅基流动API响应:', response);

            if (!response.text) {
                throw new Error('API返回数据格式错误');
            }

            console.log('语音识别成功:', response.text);

            // 获取准确的今天日期
            const accurateToday = new Date().toISOString().split('T')[0];

            // 解析语音文本，提取记账信息
            const parsedInfo = this.parseRecordInfo(response.text, accurateToday);

            return {
                text: response.text,
                duration: response.duration || 0,
                language: response.language || 'zh',
                parsedInfo: parsedInfo,
                accurateToday: accurateToday
            };

        } catch (error) {
            console.error('直接API调用失败:', error);

            // 如果API调用失败，返回一个明确的错误信息而不是模拟数据
            throw new Error('语音识别API调用失败: ' + error.message);
        }
    }

    /**
     * 直接调用硅基流动API（真实API调用）
     */
    async callSiliconFlowApiDirectly(base64Data, audioFormat, apiKey, apiUrl) {
        try {
            console.log('启用硅基流动API直接调用模式');
            console.log('音频格式:', audioFormat, '数据大小:', base64Data.length);

            // 移除base64前缀（如果存在）
            const cleanBase64 = base64Data.replace(/^data:audio\/[^;]+;base64,/, '');

            // 将base64转换为Blob
            const audioBlob = this.base64ToBlob(cleanBase64, `audio/${audioFormat}`);
            console.log('音频Blob大小:', audioBlob.size, 'bytes');

            // 构建FormData
            const formData = new FormData();
            formData.append('model', 'FunAudioLLM/SenseVoiceSmall');
            formData.append('language', 'zh');
            formData.append('file', audioBlob, `audio.${audioFormat}`);

            console.log('准备发送请求到硅基流动API...');

            // 发送请求
            const response = await uni.request({
                url: apiUrl,
                method: 'POST',
                header: {
                    'Authorization': `Bearer ${apiKey}`,
                    // 注意：不要手动设置Content-Type，让浏览器自动设置multipart/form-data的boundary
                },
                data: formData,
                timeout: 30000
            });

            console.log('硅基流动API响应:', response);

            if (response[1] && response[1].statusCode === 200) {
                const result = response[1].data;
                console.log('API调用成功:', result);

                if (result.text) {
                    return {
                        text: result.text,
                        duration: result.duration || 0,
                        language: result.language || 'zh'
                    };
                } else {
                    throw new Error('API返回数据格式错误');
                }
            } else {
                const statusCode = response[1] ? response[1].statusCode : 'unknown';
                const errorData = response[1] ? response[1].data : 'no response';
                console.error('API请求失败:', statusCode, errorData);
                throw new Error(`API请求失败: ${statusCode}`);
            }

        } catch (error) {
            console.error('硅基流动API调用失败:', error);

            // 如果是网络错误，尝试使用uni.uploadFile
            if (error.message.includes('网络') || error.message.includes('timeout')) {
                console.log('尝试使用uni.uploadFile方式...');
                return await this.callApiWithUploadFile(base64Data, audioFormat, apiKey, apiUrl);
            }

            throw new Error('语音识别API调用失败: ' + error.message);
        }
    }

    /**
     * 使用uni.uploadFile调用API（备用方案）
     */
    async callApiWithUploadFile(base64Data, audioFormat, apiKey, apiUrl) {
        try {
            console.log('使用uni.uploadFile方式调用API');

            // 创建临时文件
            const tempFilePath = await this.createTempAudioFile(base64Data, audioFormat);
            console.log('临时文件创建成功:', tempFilePath);

            return new Promise((resolve, reject) => {
                uni.uploadFile({
                    url: apiUrl,
                    filePath: tempFilePath,
                    name: 'file',
                    formData: {
                        'model': 'FunAudioLLM/SenseVoiceSmall',
                        'language': 'zh'
                    },
                    header: {
                        'Authorization': `Bearer ${apiKey}`
                    },
                    timeout: 30000,
                    success: (res) => {
                        console.log('uni.uploadFile成功:', res);

                        try {
                            const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;

                            if (res.statusCode === 200 && data.text) {
                                resolve({
                                    text: data.text,
                                    duration: data.duration || 0,
                                    language: data.language || 'zh'
                                });
                            } else {
                                reject(new Error(`API错误: ${res.statusCode} - ${JSON.stringify(data)}`));
                            }
                        } catch (e) {
                            reject(new Error('解析API响应失败: ' + e.message));
                        }
                    },
                    fail: (error) => {
                        console.error('uni.uploadFile失败:', error);
                        reject(new Error('文件上传失败: ' + error.errMsg));
                    }
                });
            });

        } catch (error) {
            console.error('uploadFile方式也失败:', error);
            throw error;
        }
    }

    /**
     * 创建临时音频文件
     */
    async createTempAudioFile(base64Data, audioFormat) {
        return new Promise((resolve, reject) => {
            // 移除base64前缀
            const cleanBase64 = base64Data.replace(/^data:audio\/[^;]+;base64,/, '');

            // 生成临时文件名
            const tempFileName = `temp_audio_${Date.now()}.${audioFormat}`;

            // 根据平台选择不同的路径
            let tempFilePath;
            // #ifdef APP-PLUS
            tempFilePath = `_doc/uniapp_temp/${tempFileName}`;
            // #endif
            // #ifdef H5
            tempFilePath = tempFileName;
            // #endif
            // #ifdef MP-WEIXIN
            tempFilePath = `${wx.env.USER_DATA_PATH}/${tempFileName}`;
            // #endif

            console.log('创建临时文件:', tempFilePath);

            const fs = uni.getFileSystemManager();
            fs.writeFile({
                filePath: tempFilePath,
                data: cleanBase64,
                encoding: 'base64',
                success: () => {
                    console.log('临时文件写入成功');
                    resolve(tempFilePath);
                },
                fail: (error) => {
                    console.error('临时文件写入失败:', error);
                    reject(new Error('创建临时文件失败: ' + error.errMsg));
                }
            });
        });
    }

    /**
     * 将base64转换为Blob
     */
    base64ToBlob(base64Data, mimeType) {
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: mimeType });
    }

    /**
     * 将base64字符串转换为ArrayBuffer
     */
    base64ToArrayBuffer(base64) {
        const binaryString = atob(base64);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return bytes.buffer;
    }

    /**
     * 生成随机字符串
     */
    generateRandomString(length) {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * 构建multipart/form-data请求体（uni-app兼容版本）
     */
    buildMultipartFormData(audioBuffer, audioFormat, boundary) {
        // uni-app兼容的字符串转字节数组方法
        const stringToBytes = (str) => {
            const bytes = new Uint8Array(str.length);
            for (let i = 0; i < str.length; i++) {
                bytes[i] = str.charCodeAt(i);
            }
            return bytes;
        };

        // 构建各个部分
        const filePartStr =
            `--${boundary}\r\n` +
            `Content-Disposition: form-data; name="file"; filename="audio.${audioFormat}"\r\n` +
            `Content-Type: audio/${audioFormat}\r\n\r\n`;
        const filePart = stringToBytes(filePartStr);

        const modelPartStr =
            `\r\n--${boundary}\r\n` +
            `Content-Disposition: form-data; name="model"\r\n\r\n` +
            `FunAudioLLM/SenseVoiceSmall\r\n`;
        const modelPart = stringToBytes(modelPartStr);

        const languagePartStr =
            `--${boundary}\r\n` +
            `Content-Disposition: form-data; name="language"\r\n\r\n` +
            `zh\r\n`;
        const languagePart = stringToBytes(languagePartStr);

        const endPartStr = `--${boundary}--\r\n`;
        const endPart = stringToBytes(endPartStr);

        // 合并所有部分
        const totalLength = filePart.byteLength + audioBuffer.byteLength +
                           modelPart.byteLength + languagePart.byteLength + endPart.byteLength;

        const result = new Uint8Array(totalLength);
        let offset = 0;

        result.set(filePart, offset);
        offset += filePart.byteLength;

        result.set(new Uint8Array(audioBuffer), offset);
        offset += audioBuffer.byteLength;

        result.set(modelPart, offset);
        offset += modelPart.byteLength;

        result.set(languagePart, offset);
        offset += languagePart.byteLength;

        result.set(endPart, offset);

        return result.buffer;
    }

    /**
     * 发送HTTP请求（uni-app兼容版本）
     */
    async makeHttpRequest(url, options) {
        return new Promise((resolve, reject) => {
            // 使用uni.request发送请求
            uni.request({
                url: url,
                method: options.method,
                header: options.headers,
                data: options.body,
                dataType: 'json',
                responseType: 'text',
                timeout: 30000,
                success: (res) => {
                    console.log('uni.request响应:', res);

                    if (res.statusCode === 200) {
                        try {
                            // 如果返回的是字符串，尝试解析为JSON
                            const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
                            resolve(data);
                        } catch (e) {
                            resolve(res.data);
                        }
                    } else {
                        reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
                    }
                },
                fail: (error) => {
                    console.error('uni.request失败:', error);
                    reject(new Error(`请求失败: ${error.errMsg || error.message}`));
                }
            });
        });
    }

    /**
     * 解析语音文本，提取记账信息（简化版本）
     */
    parseRecordInfo(text, accurateToday) {
        console.log('解析语音文本:', text);

        // 金额匹配
        const amountPatterns = [
            /(\d+(?:\.\d+)?)[元块钱]/,
            /(\d+(?:\.\d+)?)块/,
            /(\d+(?:\.\d+)?)元/,
            /花了?(\d+(?:\.\d+)?)/,
            /(\d+(?:\.\d+)?)$/,
            /收入(\d+(?:\.\d+)?)/,
            /(\d+(?:\.\d+)?)收入/
        ];

        let amount = null;
        for (const pattern of amountPatterns) {
            const match = text.match(pattern);
            if (match) {
                amount = parseFloat(match[1]);
                console.log('匹配到金额:', amount);
                break;
            }
        }

        if (!amount || amount <= 0) {
            console.log('未匹配到有效金额');
            return null;
        }

        // 收支类型识别
        const incomeKeywords = ['工资', '收入', '奖金', '红包', '薪水', '薪资'];
        const isIncome = incomeKeywords.some(keyword => text.includes(keyword));
        const type = isIncome ? '收入' : '支出';

        // 简单的分类识别
        let category = '其他';
        if (isIncome) {
            category = '工资';
        } else {
            const categoryMap = {
                '餐饮': ['吃', '餐', '饭', '菜', '食', '喝', '咖啡', '奶茶'],
                '交通': ['车', '油', '停车', '地铁', '公交', '打车', '滴滴'],
                '购物': ['买', '购', '商场', '超市', '淘宝', '京东'],
                '娱乐': ['电影', '游戏', 'KTV', '娱乐', '玩'],
                '生活': ['水电', '房租', '物业', '话费', '网费']
            };

            for (const [cat, keywords] of Object.entries(categoryMap)) {
                if (keywords.some(keyword => text.includes(keyword))) {
                    category = cat;
                    break;
                }
            }
        }

        return {
            type: type,
            amount: amount,
            category: category,
            description: text,
            date: accurateToday,
            categoryId: '',
            note: text
        };
    }

    /**
     * Blob转base64
     */
    blobToBase64(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.isRecording = false;
        this.recordingStartTime = null;
        
        if (this.mediaRecorder) {
            const stream = this.mediaRecorder.stream;
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
            this.mediaRecorder = null;
            this.audioChunks = [];
        }
        
        if (this.recorderManager) {
            this.recorderManager = null;
        }
    }

    /**
     * 获取录音状态
     */
    getRecordingStatus() {
        return this.isRecording;
    }
}

// 创建单例实例
const speechRecognitionCompatService = new SpeechRecognitionCompatService();

export default speechRecognitionCompatService;
